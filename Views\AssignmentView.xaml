<UserControl x:Class="DriverManagementSystem.Views.AssignmentView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             FlowDirection="LeftToRight"
             Background="White">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/PrintStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Converter للتحقق من وجود الصور -->
            <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

            <!-- Converter مخصص للتحقق من وجود النص/الصورة -->
            <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>

            <!-- Converter للتحقق من النص الفارغ -->
            <local:EmptyStringToVisibilityConverter x:Key="EmptyStringToVisibilityConverter"/>

        </ResourceDictionary>
    </UserControl.Resources>

    <!-- الإطار الخارجي الأسود بحجم A4 مصغر -->
    <Border BorderBrush="Black" BorderThickness="3" Margin="10" Background="White"
            Width="600" Height="850" HorizontalAlignment="Center" VerticalAlignment="Top">

        <!-- Container الرئيسي -->
        <StackPanel Background="White" Margin="20" FlowDirection="LeftToRight">

            <!-- الهيدر -->
            <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1"
                    Padding="10" Margin="0,0,0,20">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- الشعار -->
                    <Image Grid.Column="1" Source="../icons/sfd.png"
                           Width="91" Height="57"
                           VerticalAlignment="Top"
                           HorizontalAlignment="Left"
                           Margin="243,17,0,0" Grid.ColumnSpan="2"/>

                    <!-- النصوص الإنجليزية -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Top" HorizontalAlignment="Left" Margin="4,10,0,0">
                        <TextBlock Text="Social Fund For Development"
                                   FontSize="12" FontWeight="Bold"
                                   Foreground="#666666" Margin="0,2"
                                   TextAlignment="Center"/>
                        <TextBlock Text="Republic OF YEMEN"
                                   FontSize="11"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                        <TextBlock Text="Presidency of Council of Ministers"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                        <TextBlock Text="Dhamar &amp;Albidaa Branch"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                    </StackPanel>

                    <!-- النصوص العربية -->
                    <StackPanel Grid.Column="2" VerticalAlignment="Top" HorizontalAlignment="Left" Margin="157,10,0,0">
                        <TextBlock Text="الجمهورية اليمنية"
                                   FontSize="12" FontWeight="Bold"
                                   Foreground="#666666" Margin="0,2"
                                   TextAlignment="Center"/>
                        <TextBlock Text="رئاسة مجلس الوزراء"
                                   FontSize="11"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                        <TextBlock Text="الصندوق الاجتماعي للتنمية"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                        <TextBlock Text="فرع ذمار البيضاء"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- رقم الزيارة فقط -->

        </StackPanel>
    </Border>
</UserControl>
