<UserControl x:Class="DriverManagementSystem.Views.AssignmentView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             FlowDirection="LeftToRight"
             Background="White">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/PrintStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Converter للتحقق من وجود الصور -->
            <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

            <!-- Converter مخصص للتحقق من وجود النص/الصورة -->
            <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>

            <!-- Converter للتحقق من النص الفارغ -->
            <local:EmptyStringToVisibilityConverter x:Key="EmptyStringToVisibilityConverter"/>

        </ResourceDictionary>
    </UserControl.Resources>

    <!-- الإطار الخارجي الأسود بحجم A4 مصغر -->
    <Border BorderBrush="Black" BorderThickness="3" Margin="10" Background="White"
            Width="600" Height="850" HorizontalAlignment="Center" VerticalAlignment="Top">

        <!-- Container الرئيسي -->
        <StackPanel Background="Transparent" Margin="20,20,20,7" FlowDirection="LeftToRight">

            <!-- الهيدر -->
            <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1"
                    Padding="10" Margin="0,0,0,20">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- الشعار -->
                    <Image Grid.Column="1" Source="../icons/sfd.png"
                           Width="91" Height="57"
                           VerticalAlignment="Top"
                           HorizontalAlignment="Left"
                           Margin="243,17,0,0" Grid.ColumnSpan="2"/>

                    <!-- النصوص الإنجليزية -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Top" HorizontalAlignment="Left" Margin="4,10,0,0">
                        <TextBlock Text="Social Fund For Development"
                                   FontSize="12" FontWeight="Bold"
                                   Foreground="#666666" Margin="0,2"
                                   TextAlignment="Center"/>
                        <TextBlock Text="Republic OF YEMEN"
                                   FontSize="11"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                        <TextBlock Text="Presidency of Council of Ministers"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                        <TextBlock Text="Dhamar &amp;Albidaa Branch"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                    </StackPanel>

                    <!-- النصوص العربية -->
                    <StackPanel Grid.Column="2" VerticalAlignment="Top" HorizontalAlignment="Left" Margin="157,10,0,0">
                        <TextBlock Text="الجمهورية اليمنية"
                                   FontSize="12" FontWeight="Bold"
                                   Foreground="#666666" Margin="0,2"
                                   TextAlignment="Center"/>
                        <TextBlock Text="رئاسة مجلس الوزراء"
                                   FontSize="11"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                        <TextBlock Text="الصندوق الاجتماعي للتنمية"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                        <TextBlock Text="فرع ذمار البيضاء"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Canvas الجسم -->
            <Canvas x:Name="BodyCanvas" Width="560" Height="614">

                <!-- Title -->
                <Thumb x:Name="TitleThumb"
                       Canvas.Left="253" Canvas.Top="10"
                       DragDelta="DragDelta" HorizontalAlignment="Left" VerticalAlignment="Center" Width="82">
                    <Thumb.Template>
                        <ControlTemplate TargetType="Thumb">
                            <TextBlock Text="تكليف" FontSize="20"
                                       FontWeight="Bold" TextAlignment="Center"/>
                        </ControlTemplate>
                    </Thumb.Template>
                </Thumb>

                <!-- Intro paragraph -->
                <Thumb x:Name="ParagraphThumb"
                       Canvas.Left="20" Canvas.Top="50"
                       Width="520" DragDelta="DragDelta">
                    <Thumb.Template>
                        <ControlTemplate TargetType="Thumb">
                            <TextBlock FlowDirection="RightToLeft"
                                       TextWrapping="Wrap" FontSize="13">
                                يُكلف الصندوق الاجتماعي للتنمية – فرع ذمار البيضاء الأخ/
                                <Run Text="{Binding AssigneeName, FallbackValue=''}"
                                     FontWeight="Bold"/>
                                المبين أسمه في الجدول أدناه لتنفيذ المهمة التالية:
                            </TextBlock>
                        </ControlTemplate>
                    </Thumb.Template>
                </Thumb>

                <!-- Project / Activity / Route -->
                <Thumb x:Name="FieldsThumb"
                       Canvas.Left="20" Canvas.Top="110"
                       Width="520" DragDelta="DragDelta">
                    <Thumb.Template>
                        <ControlTemplate TargetType="Thumb">
                            <UniformGrid Columns="3">
                                <Border BorderBrush="#999" BorderThickness="1" Padding="4">
                                    <StackPanel FlowDirection="RightToLeft">
                                        <TextBlock Text="المشروع" FontWeight="Bold"
                                                   FontSize="12" TextAlignment="Center"/>
                                        <TextBlock Text="{Binding ProjectName, FallbackValue=---}"
                                                   FontSize="12" TextAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                <Border BorderBrush="#999" BorderThickness="1" Padding="4">
                                    <StackPanel FlowDirection="RightToLeft">
                                        <TextBlock Text="النشاط" FontWeight="Bold"
                                                   FontSize="12" TextAlignment="Center"/>
                                        <TextBlock Text="{Binding ActivityName, FallbackValue=---}"
                                                   FontSize="12" TextAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                <Border BorderBrush="#999" BorderThickness="1" Padding="4">
                                    <StackPanel FlowDirection="RightToLeft">
                                        <TextBlock Text="خط السير" FontWeight="Bold"
                                                   FontSize="12" TextAlignment="Center"/>
                                        <TextBlock Text="{Binding RouteName, FallbackValue=---}"
                                                   FontSize="12" TextAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </UniformGrid>
                        </ControlTemplate>
                    </Thumb.Template>
                </Thumb>

                <!-- Assignees table -->
                <Thumb x:Name="AssigneesThumb"
                       Canvas.Left="20" Canvas.Top="180"
                       Width="520" Height="150" DragDelta="DragDelta">
                    <Thumb.Template>
                        <ControlTemplate TargetType="Thumb">
                            <DataGrid ItemsSource="{Binding Assignees}"
                                      AutoGenerateColumns="False" HeadersVisibility="Column"
                                      FontSize="11" RowHeight="22">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="م" Binding="{Binding Index}" Width="35"/>
                                    <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="*"/>
                                    <DataGridTextColumn Header="رقم الهوية" Binding="{Binding IdNumber}" Width="100"/>
                                    <DataGridTextColumn Header="نوع الهوية" Binding="{Binding IdType}" Width="80"/>
                                    <DataGridTextColumn Header="رقم الجوال" Binding="{Binding Mobile}" Width="95"/>
                                    <DataGridTextColumn Header="الصفة" Binding="{Binding Position}" Width="80"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </ControlTemplate>
                    </Thumb.Template>
                </Thumb>

                <!-- Vehicles table -->
                <Thumb x:Name="VehiclesThumb"
                       Canvas.Left="20" Canvas.Top="340"
                       Width="520" Height="120" DragDelta="DragDelta">
                    <Thumb.Template>
                        <ControlTemplate TargetType="Thumb">
                            <DataGrid ItemsSource="{Binding Vehicles}"
                                      AutoGenerateColumns="False" HeadersVisibility="Column"
                                      FontSize="11" RowHeight="22">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="م" Binding="{Binding Index}" Width="35"/>
                                    <DataGridTextColumn Header="اسم السائق" Binding="{Binding DriverName}" Width="*"/>
                                    <DataGridTextColumn Header="رقم اللوحة" Binding="{Binding PlateNumber}" Width="85"/>
                                    <DataGridTextColumn Header="نوع السيارة" Binding="{Binding VehicleType}" Width="90"/>
                                    <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="90"/>
                                    <DataGridTextColumn Header="رقم الهوية" Binding="{Binding DriverId}" Width="100"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </ControlTemplate>
                    </Thumb.Template>
                </Thumb>

                <!-- Dates -->
                <Thumb x:Name="DatesThumb"
                       Canvas.Left="120" Canvas.Top="475"
                       Width="320" DragDelta="DragDelta">
                    <Thumb.Template>
                        <ControlTemplate TargetType="Thumb">
                            <Grid FlowDirection="RightToLeft">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="25"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="تاريخ التحرك" FontWeight="Bold" FontSize="12"/>
                                    <TextBlock Text="{Binding StartDate, FallbackValue='____/__/__'}"
                                               TextDecorations="Underline" FontSize="12"
                                               TextAlignment="Center"/>
                                </StackPanel>

                                <StackPanel Grid.Column="2">
                                    <TextBlock Text="تاريخ العودة" FontWeight="Bold" FontSize="12"/>
                                    <TextBlock Text="{Binding EndDate, FallbackValue='____/__/__'}"
                                               TextDecorations="Underline" FontSize="12"
                                               TextAlignment="Center"/>
                                </StackPanel>
                            </Grid>
                        </ControlTemplate>
                    </Thumb.Template>
                </Thumb>

                <!-- Signature -->
                <Thumb x:Name="SignatureThumb"
                       Canvas.Left="20" Canvas.Top="530"
                       Width="520" DragDelta="DragDelta">
                    <Thumb.Template>
                        <ControlTemplate TargetType="Thumb">
                            <Grid FlowDirection="RightToLeft">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0" HorizontalAlignment="Left">
                                    <TextBlock Text="مدير الفرع" FontWeight="Bold" FontSize="12"/>
                                </StackPanel>
                                <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                                    <TextBlock Text="م/محمد محمد الديلمي" FontWeight="Bold" FontSize="12"/>
                                </StackPanel>
                            </Grid>
                        </ControlTemplate>
                    </Thumb.Template>
                </Thumb>

            </Canvas>

            <!-- Footer قابل للتحريك -->
            <Canvas>
                <Thumb Name="DraggableFooter"
                       Canvas.Left="-16" Canvas.Top="10"
                       Width="587" Height="134"
                       Cursor="SizeAll"
                       DragDelta="DraggableFooter_DragDelta" HorizontalAlignment="Center" VerticalAlignment="Top">
                    <Thumb.Template>
                        <ControlTemplate TargetType="Thumb">
                            <Border Background="Transparent" BorderBrush="#DEE2E6"
                                    CornerRadius="0" Padding="0" Margin="0,7,0,-20">

                                <StackPanel Margin="0,0,0,66">

                                    <!-- النص العربي -->
                                    <TextBlock Text="الصندوق الاجتماعي للتنمية - فرع ذمار - خط صنعاء تعز - جولة كمران - عمارة الأوقاف - هاتف : 503045 / فاكس: 503047 / ص.ب: 87210"
                                               FontSize="10" FontWeight="Bold" TextAlignment="Center"
                                               Foreground="#2C3E50" Margin="0,2" FlowDirection="RightToLeft"
                                               TextWrapping="Wrap"/>

                                    <TextBlock Text="الرقم المجاني للشكاوي 8009800 أو الرقم المباشر بالشكاوي 770959624."
                                               FontSize="10" TextAlignment="Center"
                                               Foreground="#2C3E50" Margin="0,2" FlowDirection="RightToLeft"/>

                                    <!-- النص الإنجليزي -->
                                    <TextBlock FontSize="10" TextAlignment="Center" Foreground="#2C3E50"
                                               Margin="0,2" FlowDirection="LeftToRight" TextWrapping="Wrap">
                                        <Run Text="Social Fund for Development / Dhamar Branch – Taiz Sana'a Street- Camran Round – P.O. Box: 87400 "/>
                                        <Run Text="Tel: 503045" Foreground="Blue" TextDecorations="Underline"/>
                                        <Run Text=" – Fax: 503047 – Free number: 8009800 – Email: <EMAIL>"/>
                                    </TextBlock>
                                </StackPanel>
                            </Border>
                        </ControlTemplate>
                    </Thumb.Template>
                </Thumb>
            </Canvas>

        </StackPanel>
    </Border>
</UserControl>
