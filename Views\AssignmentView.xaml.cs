using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// Interaction logic for AssignmentView.xaml
    /// </summary>
    public partial class AssignmentView : UserControl
    {
        public AssignmentView()
        {
            InitializeComponent();
            // إنشاء ViewModel افتراضي فقط إذا لم يكن هناك DataContext
            if (DataContext == null)
            {
                DataContext = new ReportViewModel();
            }
        }

        /// <summary>
        /// Constructor مع DataContext محدد مسبقاً
        /// </summary>
        public AssignmentView(ReportViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
        }

        /// <summary>
        /// معالج حدث سحب العناصر القابلة للتحريك
        /// </summary>
        private void DragDelta(object sender, DragDeltaEventArgs e)
        {
            if (sender is Thumb thumb)
            {
                // الحصول على الموقع الحالي
                double newLeft = Canvas.GetLeft(thumb) + e.HorizontalChange;
                double newTop = Canvas.GetTop(thumb) + e.VerticalChange;

                // التأكد من أن العنصر لا يخرج من حدود Canvas
                var canvas = thumb.Parent as Canvas;
                if (canvas != null)
                {
                    if (newLeft >= 0 && newLeft <= (canvas.Width - thumb.Width))
                    {
                        Canvas.SetLeft(thumb, newLeft);
                    }

                    if (newTop >= 0 && newTop <= (canvas.Height - thumb.Height))
                    {
                        Canvas.SetTop(thumb, newTop);
                    }
                }
            }
        }

        /// <summary>
        /// معالج حدث سحب الفوتر القابل للتحريك
        /// </summary>
        private void DraggableFooter_DragDelta(object sender, DragDeltaEventArgs e)
        {
            if (sender is Thumb thumb)
            {
                // الحصول على الموقع الحالي
                double newLeft = Canvas.GetLeft(thumb) + e.HorizontalChange;
                double newTop = Canvas.GetTop(thumb) + e.VerticalChange;

                // التأكد من أن العنصر لا يخرج من حدود النافذة
                if (newLeft >= 0 && newLeft <= (ActualWidth - thumb.Width))
                {
                    Canvas.SetLeft(thumb, newLeft);
                }

                if (newTop >= 0 && newTop <= (ActualHeight - thumb.Height))
                {
                    Canvas.SetTop(thumb, newTop);
                }
            }
        }
    }
}
